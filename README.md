# TNGD Backup System

Clean, organized backup system for TNGD data with clear logging and reliable operation.

## Project Structure

```
TNGD/
├── tngd_backup.py          # Main backup script
├── requirements.txt        # Python dependencies
├── config/                 # Configuration files
│   ├── config.json        # System configuration
│   └── tables.json        # Table definitions
├── core/                   # Core modules
│   ├── config_manager.py  # Configuration management
│   ├── devo_client.py     # Devo API client
│   ├── storage_manager.py # OSS storage management
│   ├── email_service.py   # Email notifications
│   └── compression_service.py # File compression
├── tests/                  # Test files
│   └── test_email_service.py # Email service tests
├── docs/                   # Documentation
│   └── sample_email_report.html # Sample email report
├── logs/                   # Log files (auto-generated)
└── temp/                   # Temporary files (auto-generated)
```

## Usage

### Basic Usage
```bash
# Backup today's data
python tngd_backup.py

# Backup specific date
python tngd_backup.py 2025-03-01

# Backup date range
python tngd_backup.py 2025-03-01 2025-03-31
```

### Configuration

1. **Environment Variables**: Create a `.env` file with your credentials:
   ```
   DEVO_API_KEY=your_devo_api_key
   DEVO_API_SECRET=your_devo_api_secret
   OSS_ACCESS_KEY_ID=your_oss_access_key
   OSS_ACCESS_KEY_SECRET=your_oss_secret_key
   OSS_ENDPOINT=your_oss_endpoint
   OSS_BUCKET_NAME=your_bucket_name
   SMTP_SENDER=<EMAIL>
   SMTP_PASSWORD=your_email_password
   SMTP_RECEIVER=<EMAIL>
   ```

2. **System Configuration**: Edit `config/config.json` for system settings
3. **Table Configuration**: Edit `config/tables.json` to define which tables to backup

### Testing

```bash
# Test email service
cd tests
python test_email_service.py
```

## Features

- ✅ Clean, organized project structure
- ✅ Automatic configuration file detection
- ✅ Comprehensive logging with unique log files per run
- ✅ Email notifications with detailed reports
- ✅ Robust error handling and retry logic
- ✅ OSS storage with compression
- ✅ Support for date ranges and single dates
- ✅ Backward compatibility with legacy file locations

## Requirements

- Python 3.7+
- See `requirements.txt` for Python dependencies
- Valid Devo API credentials
- OSS storage credentials
- SMTP credentials (optional, for email notifications)
