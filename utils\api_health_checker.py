#!/usr/bin/env python3
"""
API Health Checker Module

This module provides utilities to check the health and connectivity of external APIs
used by the TNGD backup system, particularly the Devo API.

Features:
- API connectivity testing
- Response time measurement
- Authentication validation
- Service status reporting
- Configuration validation
"""

import logging
import time
import requests
from typing import Dict, Any, Optional
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

# Configure logging
logger = logging.getLogger(__name__)


class HealthStatus(Enum):
    """Health status levels."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


@dataclass
class HealthCheckResult:
    """Result of a health check."""
    service_name: str
    status: HealthStatus
    response_time_ms: float
    timestamp: datetime
    message: str
    details: Dict[str, Any]
    error: Optional[Exception] = None


class APIHealthChecker:
    """Health checker for external APIs."""
    
    def __init__(self, config_manager=None):
        """
        Initialize the API health checker.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config_manager = config_manager
        self.timeout_seconds = 30
        
        # Load configuration
        if config_manager:
            try:
                data_source_config = config_manager.config.get('data_source', {})
                connection_config = data_source_config.get('connection', {})
                self.timeout_seconds = connection_config.get('timeout_seconds', 30)
            except Exception as e:
                logger.warning(f"Could not load API configuration: {e}")
    
    def check_devo_api_health(self) -> HealthCheckResult:
        """
        Check the health of the Devo API.
        
        Returns:
            HealthCheckResult with API status
        """
        service_name = "Devo API"
        start_time = time.time()
        
        try:
            # Get Devo credentials
            if not self.config_manager:
                return HealthCheckResult(
                    service_name=service_name,
                    status=HealthStatus.UNKNOWN,
                    response_time_ms=0.0,
                    timestamp=datetime.now(),
                    message="No configuration manager available",
                    details={}
                )
            
            # Check if Devo credentials are available
            # Since there's no get_devo_credentials method, check environment directly
            devo_creds = {
                'api_key': self.config_manager.get_env('DEVO_API_KEY'),
                'api_secret': self.config_manager.get_env('DEVO_API_SECRET'),
                'url': self.config_manager.get_env('DEVO_URL')
            }

            if not devo_creds or not all([
                devo_creds.get('api_key'),
                devo_creds.get('api_secret'),
                devo_creds.get('url')
            ]):
                return HealthCheckResult(
                    service_name=service_name,
                    status=HealthStatus.UNHEALTHY,
                    response_time_ms=0.0,
                    timestamp=datetime.now(),
                    message="Missing Devo API credentials",
                    details={"missing_credentials": True}
                )
            
            # Test basic connectivity (without actual API call)
            api_url = devo_creds.get('url', '')
            if not api_url.startswith(('http://', 'https://')):
                return HealthCheckResult(
                    service_name=service_name,
                    status=HealthStatus.UNHEALTHY,
                    response_time_ms=0.0,
                    timestamp=datetime.now(),
                    message="Invalid Devo API URL format",
                    details={"url": api_url}
                )
            
            # Simple connectivity test (ping-like)
            try:
                response = requests.head(
                    api_url,
                    timeout=min(self.timeout_seconds, 10),
                    allow_redirects=True
                )
                response_time_ms = (time.time() - start_time) * 1000
                
                if response.status_code < 400:
                    status = HealthStatus.HEALTHY
                    message = f"API endpoint accessible (HTTP {response.status_code})"
                elif response.status_code < 500:
                    status = HealthStatus.DEGRADED
                    message = f"API endpoint returned client error (HTTP {response.status_code})"
                else:
                    status = HealthStatus.UNHEALTHY
                    message = f"API endpoint returned server error (HTTP {response.status_code})"
                
                return HealthCheckResult(
                    service_name=service_name,
                    status=status,
                    response_time_ms=response_time_ms,
                    timestamp=datetime.now(),
                    message=message,
                    details={
                        "status_code": response.status_code,
                        "url": api_url,
                        "headers": dict(response.headers)
                    }
                )
                
            except requests.exceptions.Timeout:
                response_time_ms = (time.time() - start_time) * 1000
                return HealthCheckResult(
                    service_name=service_name,
                    status=HealthStatus.UNHEALTHY,
                    response_time_ms=response_time_ms,
                    timestamp=datetime.now(),
                    message="API endpoint timeout",
                    details={"timeout_seconds": self.timeout_seconds}
                )
                
            except requests.exceptions.ConnectionError as e:
                response_time_ms = (time.time() - start_time) * 1000
                return HealthCheckResult(
                    service_name=service_name,
                    status=HealthStatus.UNHEALTHY,
                    response_time_ms=response_time_ms,
                    timestamp=datetime.now(),
                    message="Cannot connect to API endpoint",
                    details={"connection_error": str(e)},
                    error=e
                )
                
        except Exception as e:
            response_time_ms = (time.time() - start_time) * 1000
            return HealthCheckResult(
                service_name=service_name,
                status=HealthStatus.UNKNOWN,
                response_time_ms=response_time_ms,
                timestamp=datetime.now(),
                message=f"Health check failed: {str(e)}",
                details={"error_type": type(e).__name__},
                error=e
            )
    
    def check_storage_health(self) -> HealthCheckResult:
        """
        Check the health of the storage system.
        
        Returns:
            HealthCheckResult with storage status
        """
        service_name = "Storage System"
        start_time = time.time()
        
        try:
            if not self.config_manager:
                return HealthCheckResult(
                    service_name=service_name,
                    status=HealthStatus.UNKNOWN,
                    response_time_ms=0.0,
                    timestamp=datetime.now(),
                    message="No configuration manager available",
                    details={}
                )
            
            # Check OSS credentials using the existing method
            oss_creds = self.config_manager.get_oss_credentials()

            if not oss_creds or not all([
                oss_creds.get('access_key_id'),
                oss_creds.get('access_key_secret'),
                oss_creds.get('endpoint'),
                oss_creds.get('bucket')  # Note: the method returns 'bucket', not 'bucket_name'
            ]):
                return HealthCheckResult(
                    service_name=service_name,
                    status=HealthStatus.UNHEALTHY,
                    response_time_ms=0.0,
                    timestamp=datetime.now(),
                    message="Missing OSS storage credentials",
                    details={"missing_credentials": True}
                )
            
            # Check endpoint format
            endpoint = oss_creds.get('endpoint', '')
            if not endpoint.startswith(('http://', 'https://')):
                return HealthCheckResult(
                    service_name=service_name,
                    status=HealthStatus.UNHEALTHY,
                    response_time_ms=0.0,
                    timestamp=datetime.now(),
                    message="Invalid OSS endpoint format",
                    details={"endpoint": endpoint}
                )
            
            response_time_ms = (time.time() - start_time) * 1000
            
            return HealthCheckResult(
                service_name=service_name,
                status=HealthStatus.HEALTHY,
                response_time_ms=response_time_ms,
                timestamp=datetime.now(),
                message="Storage credentials configured",
                details={
                    "endpoint": endpoint,
                    "bucket": oss_creds.get('bucket')
                }
            )
            
        except Exception as e:
            response_time_ms = (time.time() - start_time) * 1000
            return HealthCheckResult(
                service_name=service_name,
                status=HealthStatus.UNKNOWN,
                response_time_ms=response_time_ms,
                timestamp=datetime.now(),
                message=f"Storage health check failed: {str(e)}",
                details={"error_type": type(e).__name__},
                error=e
            )
    
    def run_comprehensive_health_check(self) -> Dict[str, HealthCheckResult]:
        """
        Run comprehensive health checks for all services.
        
        Returns:
            Dictionary of service names to health check results
        """
        results = {}
        
        # Check Devo API
        logger.info("Checking Devo API health...")
        results['devo_api'] = self.check_devo_api_health()
        
        # Check Storage
        logger.info("Checking storage health...")
        results['storage'] = self.check_storage_health()
        
        return results
    
    def print_health_report(self, results: Dict[str, HealthCheckResult]):
        """Print a formatted health report."""
        print("\n🏥 API Health Check Report")
        print("=" * 50)
        
        overall_status = HealthStatus.HEALTHY
        
        for _, result in results.items():
            status_emoji = {
                HealthStatus.HEALTHY: "✅",
                HealthStatus.DEGRADED: "⚠️",
                HealthStatus.UNHEALTHY: "❌",
                HealthStatus.UNKNOWN: "❓"
            }.get(result.status, "❓")
            
            print(f"{status_emoji} {result.service_name}: {result.status.value.upper()}")
            print(f"   Message: {result.message}")
            print(f"   Response Time: {result.response_time_ms:.1f}ms")
            
            if result.details:
                print(f"   Details: {result.details}")
            
            if result.error:
                print(f"   Error: {result.error}")
            
            print()
            
            # Update overall status
            if result.status == HealthStatus.UNHEALTHY:
                overall_status = HealthStatus.UNHEALTHY
            elif result.status == HealthStatus.DEGRADED and overall_status == HealthStatus.HEALTHY:
                overall_status = HealthStatus.DEGRADED
            elif result.status == HealthStatus.UNKNOWN and overall_status in [HealthStatus.HEALTHY, HealthStatus.DEGRADED]:
                overall_status = HealthStatus.UNKNOWN
        
        overall_emoji = {
            HealthStatus.HEALTHY: "✅",
            HealthStatus.DEGRADED: "⚠️",
            HealthStatus.UNHEALTHY: "❌",
            HealthStatus.UNKNOWN: "❓"
        }.get(overall_status, "❓")
        
        print(f"{overall_emoji} Overall System Status: {overall_status.value.upper()}")
        
        return overall_status


def main():
    """Main function for standalone health checking."""
    import sys
    from pathlib import Path
    
    # Add project root to path
    project_root = Path(__file__).parent.parent
    sys.path.insert(0, str(project_root))
    
    from core.config_manager import ConfigManager
    
    try:
        print("🔍 Starting API Health Check...")
        
        # Initialize configuration and health checker
        config_manager = ConfigManager()
        health_checker = APIHealthChecker(config_manager)
        
        # Run comprehensive health check
        results = health_checker.run_comprehensive_health_check()
        
        # Print report
        overall_status = health_checker.print_health_report(results)
        
        # Exit with appropriate code
        if overall_status == HealthStatus.HEALTHY:
            sys.exit(0)
        elif overall_status == HealthStatus.DEGRADED:
            sys.exit(1)
        else:
            sys.exit(2)
            
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(3)


if __name__ == "__main__":
    main()
