{"system": {"environment": "production", "version": "2.0.0", "debug_mode": true, "dry_run_mode": false}, "data_source": {"provider": "devo", "connection": {"timeout_seconds": 21600, "retry_attempts": 3, "retry_delay_seconds": 5}, "query_optimization": {"enable_query_caching": true, "cache_ttl_minutes": 30, "parallel_queries": false, "max_concurrent_queries": 1}, "table_classification": {"size_thresholds": {"small_rows": 10000, "medium_rows": 100000, "large_rows": 1000000, "very_large_rows": 10000000, "ultra_large_rows": 50000000}, "timeout_mapping": {"count_query": 120, "small_table": 600, "medium_table": 1800, "large_table": 7200, "very_large_table": 21600, "ultra_large_table": 43200}}}, "processing": {"chunking": {"enabled": true, "strategy": "time_based", "time_chunk_hours": 6, "max_rows_per_chunk": 1000000, "overlap_minutes": 5, "auto_chunk_threshold_rows": 2000000}, "deduplication": {"enabled": true, "algorithm": "efficient_hash", "memory_efficient": true, "progress_reporting_interval": 50000}, "performance": {"parallel_processing": false, "max_concurrent_operations": 1, "stream_processing": true, "memory_optimization": true}}, "backup_workflows": {"daily": {"enabled": true, "schedule_time": "01:00", "max_duration_hours": 8, "validation": {"enabled": true, "post_backup_validation": true, "checksum_verification": true}, "cleanup": {"auto_cleanup": true, "retain_temp_files_on_error": true}}, "monthly": {"enabled": true, "chunking_strategy": "daily", "checkpoint_interval_hours": 2, "validation": {"enabled": true, "validate_each_chunk": true, "final_validation": true}, "configuration_sources": {"primary_paths": ["tables.json", "config/tabletest/tables.json", "backup/tables.json"], "fallback_tables": ["my.app.tngd.waf", "my.app.tngd.actiontraillinux"], "validate_on_startup": true, "backup_config": true}}}, "error_handling": {"retry_strategy": {"max_attempts": 5, "base_delay_seconds": 30.0, "max_delay_seconds": 300.0, "exponential_backoff": true, "backoff_multiplier": 2.0, "jitter_enabled": true}, "failure_recovery": {"continue_on_table_failure": true, "skip_failed_tables": true, "auto_retry_failed_operations": true, "detailed_error_logging": true, "failure_analysis": true}, "circuit_breaker": {"enabled": true, "failure_threshold": 5, "recovery_timeout_seconds": 300, "half_open_max_calls": 3}}, "resource_management": {"memory": {"monitoring_enabled": true, "threshold_percent": 60.0, "critical_threshold_percent": 85.0, "adaptive_processing": true}, "cpu": {"monitoring_enabled": true, "threshold_percent": 70.0, "throttling_enabled": true}, "disk": {"monitoring_enabled": true, "threshold_percent": 85.0, "cleanup_on_threshold": true}, "adaptive_throttling": {"enabled": true, "min_delay_seconds": 2.0, "max_delay_seconds": 60.0, "dynamic_adjustment": true}}, "storage": {"provider": "oss", "paths": {"backup_template": "Devo/{month_name_str}/week {week_number}/{date_str}/{table_name}_{date_str}.tar.gz", "summary_template": "backup_summary_{date_str}.json", "temp_directory": "temp"}, "compression": {"algorithm": "tar.gz", "level": 6, "verification_enabled": true, "checksum_algorithm": "md5"}, "upload": {"chunk_size_mb": 100, "timeout_seconds": 3600, "retry_attempts": 3, "verify_after_upload": false, "parallel_uploads": false}, "validation": {"pre_upload_validation": true, "post_upload_validation": true, "checksum_verification": true, "retry_failed_validations": true, "max_validation_retries": 3, "validation_retry_delay_seconds": 5}}, "observability": {"logging": {"directory": "logs", "primary_log_file": "backup.log", "max_log_files": 5, "max_file_size_mb": 10, "console_output": "minimal", "structured_logging": true, "log_levels": {"console": "INFO", "file": "DEBUG"}}, "monitoring": {"enabled": true, "resource_tracking": true, "performance_metrics": true, "report_directory": "logs/performance", "alert_thresholds": {"cpu_percent": 80, "memory_percent": 90, "disk_percent": 85, "operation_timeout_seconds": 3600}}, "health_checks": {"enabled": true, "pre_operation_checks": true, "database_connectivity": true, "storage_accessibility": true, "disk_space_validation": true, "dependency_validation": true, "timeout_seconds": 30}}, "notifications": {"email": {"enabled": true, "send_on_completion": true, "send_on_error": true, "include_failed_tables": true, "include_performance_metrics": true, "max_top_tables_in_summary": 10}, "progress_updates": {"enabled": true, "interval_hours": 6, "detailed_summaries": true, "resource_usage_reports": true}}}