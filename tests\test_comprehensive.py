#!/usr/bin/env python3
"""
Comprehensive Test Suite for TNGD Backup System
===============================================

This test suite validates all core functionality including:
- Core module imports and initialization
- Configuration management
- Devo client connectivity
- Storage manager operations
- Email service functionality
- Main backup system integration
- System dependencies and environment
- Performance and resource monitoring

Usage:
    python test_comprehensive.py
"""

import os
import sys
import logging
import json
import tempfile
import shutil
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import traceback

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Add parent directory to path to import core modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Test results tracking
class TestResults:
    def __init__(self):
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        self.test_details = []
        self.start_time = datetime.now()
    
    def add_test_result(self, test_name: str, passed: bool, details: str = "", error: str = ""):
        self.total_tests += 1
        if passed:
            self.passed_tests += 1
            status = "✅ PASSED"
        else:
            self.failed_tests += 1
            status = "❌ FAILED"
        
        self.test_details.append({
            'test_name': test_name,
            'status': status,
            'passed': passed,
            'details': details,
            'error': error,
            'timestamp': datetime.now()
        })
        
        print(f"{status}: {test_name}")
        if details:
            print(f"   Details: {details}")
        if error:
            print(f"   Error: {error}")
    
    def get_summary(self):
        duration = datetime.now() - self.start_time
        return {
            'total_tests': self.total_tests,
            'passed_tests': self.passed_tests,
            'failed_tests': self.failed_tests,
            'success_rate': (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0,
            'duration_seconds': duration.total_seconds(),
            'test_details': self.test_details
        }


def setup_logging():
    """Setup logging for testing."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )


def test_core_module_imports(results: TestResults):
    """Test 1: Core module imports and initialization."""
    print("\n🧪 Testing Core Module Imports and Initialization...")
    
    # Test config_manager import
    try:
        from core.config_manager import ConfigManager
        results.add_test_result("ConfigManager Import", True, "Successfully imported ConfigManager")
    except Exception as e:
        results.add_test_result("ConfigManager Import", False, error=str(e))
    
    # Test devo_client import
    try:
        from core.devo_client import DevoClient
        results.add_test_result("DevoClient Import", True, "Successfully imported DevoClient")
    except Exception as e:
        results.add_test_result("DevoClient Import", False, error=str(e))
    
    # Test storage_manager import
    try:
        from core.storage_manager import StorageManager
        results.add_test_result("StorageManager Import", True, "Successfully imported StorageManager")
    except Exception as e:
        results.add_test_result("StorageManager Import", False, error=str(e))
    
    # Test email_service import
    try:
        from core.email_service import EmailService
        results.add_test_result("EmailService Import", True, "Successfully imported EmailService")
    except Exception as e:
        results.add_test_result("EmailService Import", False, error=str(e))
    
    # Test compression_service import
    try:
        from core.compression_service import CompressionService
        results.add_test_result("CompressionService Import", True, "Successfully imported CompressionService")
    except Exception as e:
        results.add_test_result("CompressionService Import", False, error=str(e))


def test_configuration_management(results: TestResults):
    """Test 2: Configuration management functionality."""
    print("\n🧪 Testing Configuration Management...")
    
    try:
        from core.config_manager import ConfigManager
        
        # Test config loading
        try:
            config_manager = ConfigManager()
            results.add_test_result("ConfigManager Initialization", True, "ConfigManager initialized successfully")
        except Exception as e:
            results.add_test_result("ConfigManager Initialization", False, error=str(e))
            return
        
        # Test config validation
        try:
            config = config_manager.config
            if config and isinstance(config, dict):
                results.add_test_result("Config Loading", True, f"Config loaded with {len(config)} sections")
            else:
                results.add_test_result("Config Loading", False, "Config is empty or invalid")
        except Exception as e:
            results.add_test_result("Config Loading", False, error=str(e))

        # Test tables configuration (using TngdBackup class)
        try:
            from tngd_backup import TngdBackup
            backup_system = TngdBackup()
            tables = backup_system.load_tables()
            if tables and isinstance(tables, list):
                results.add_test_result("Tables Loading", True, f"Loaded {len(tables)} tables")
            else:
                results.add_test_result("Tables Loading", False, "Tables list is empty or invalid")
        except Exception as e:
            results.add_test_result("Tables Loading", False, error=str(e))
            
    except Exception as e:
        results.add_test_result("Configuration Management", False, error=str(e))


def test_environment_variables(results: TestResults):
    """Test 3: Environment variables validation."""
    print("\n🧪 Testing Environment Variables...")
    
    required_vars = [
        'DEVO_API_KEY',
        'DEVO_API_SECRET', 
        'DEVO_QUERY_ENDPOINT',
        'OSS_ACCESS_KEY_ID',
        'OSS_ACCESS_KEY_SECRET',
        'OSS_ENDPOINT',
        'OSS_BUCKET_NAME',
        'SMTP_SERVER',
        'SMTP_PORT',
        'SMTP_SENDER',
        'SMTP_PASSWORD',
        'SMTP_RECEIVER'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if not missing_vars:
        results.add_test_result("Environment Variables", True, f"All {len(required_vars)} required variables are set")
    else:
        results.add_test_result("Environment Variables", False, f"Missing variables: {', '.join(missing_vars)}")


def test_devo_client_connectivity(results: TestResults):
    """Test 4: Devo client connectivity and functionality."""
    print("\n🧪 Testing Devo Client Connectivity...")
    
    try:
        from core.devo_client import DevoClient
        
        # Test client initialization
        try:
            devo_client = DevoClient()
            results.add_test_result("DevoClient Initialization", True, "DevoClient initialized successfully")
        except Exception as e:
            results.add_test_result("DevoClient Initialization", False, error=str(e))
            return
        
        # Test connection with a simple query
        try:
            test_query = "from my.app.tngd.waf select * limit 1"
            result = devo_client.execute_query(test_query, timeout=30)
            if result is not None:
                results.add_test_result("Devo Connection Test", True, "Connection to Devo API successful")
            else:
                results.add_test_result("Devo Connection Test", False, "Query returned None")
        except Exception as e:
            results.add_test_result("Devo Connection Test", False, error=str(e))
            
    except Exception as e:
        results.add_test_result("Devo Client Connectivity", False, error=str(e))


def test_storage_manager_operations(results: TestResults):
    """Test 5: Storage manager operations."""
    print("\n🧪 Testing Storage Manager Operations...")

    try:
        from core.storage_manager import StorageManager

        # Test storage manager initialization
        try:
            storage_manager = StorageManager()
            results.add_test_result("StorageManager Initialization", True, "StorageManager initialized successfully")
        except Exception as e:
            results.add_test_result("StorageManager Initialization", False, error=str(e))
            return

        # Test OSS connection
        try:
            if storage_manager.test_connection():
                results.add_test_result("OSS Connection Test", True, "OSS connection successful")
            else:
                results.add_test_result("OSS Connection Test", False, "OSS connection test failed")
        except Exception as e:
            results.add_test_result("OSS Connection Test", False, error=str(e))

    except Exception as e:
        results.add_test_result("Storage Manager Operations", False, error=str(e))


def test_email_service_functionality(results: TestResults):
    """Test 6: Email service functionality."""
    print("\n🧪 Testing Email Service Functionality...")

    try:
        from core.email_service import EmailService

        # Test email service initialization
        try:
            email_service = EmailService()
            results.add_test_result("EmailService Initialization", True, "EmailService initialized successfully")
        except Exception as e:
            results.add_test_result("EmailService Initialization", False, error=str(e))
            return

        # Test email connection
        try:
            if email_service.test_email_connection():
                results.add_test_result("Email Connection Test", True, "Email connection successful")
            else:
                results.add_test_result("Email Connection Test", False, "Email connection test failed")
        except Exception as e:
            results.add_test_result("Email Connection Test", False, error=str(e))

        # Test template rendering
        try:
            test_results = {
                'status': 'completed',
                'total_dates': 1,
                'total_tables': 2,
                'overall_duration': 300,
                'total_rows_backed_up': 1000,
                'date_results': []
            }
            html_content = email_service._create_backup_summary_html(test_results)
            if html_content and len(html_content) > 100:
                results.add_test_result("Email Template Rendering", True, f"Template rendered ({len(html_content)} chars)")
            else:
                results.add_test_result("Email Template Rendering", False, "Template rendering failed")
        except Exception as e:
            results.add_test_result("Email Template Rendering", False, error=str(e))

    except Exception as e:
        results.add_test_result("Email Service Functionality", False, error=str(e))


def test_compression_service(results: TestResults):
    """Test 7: Compression service functionality."""
    print("\n🧪 Testing Compression Service...")

    try:
        from core.compression_service import CompressionService

        # Test compression service initialization
        try:
            compression_service = CompressionService()
            results.add_test_result("CompressionService Initialization", True, "CompressionService initialized successfully")
        except Exception as e:
            results.add_test_result("CompressionService Initialization", False, error=str(e))
            return

        # Test compression with temporary files
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                # Create test directory with file
                test_dir = os.path.join(temp_dir, "test_data")
                os.makedirs(test_dir)
                test_file = os.path.join(test_dir, "test.txt")
                with open(test_file, 'w') as f:
                    f.write("Test data for compression")

                # Test compression
                compressed_file = os.path.join(temp_dir, "test.tar.gz")
                success, _, stats = compression_service.compress_directory(test_dir, compressed_file)
                if success:
                    results.add_test_result("Directory Compression", True, "Directory compressed successfully")
                else:
                    results.add_test_result("Directory Compression", False, "Directory compression failed")
        except Exception as e:
            results.add_test_result("Directory Compression", False, error=str(e))

    except Exception as e:
        results.add_test_result("Compression Service", False, error=str(e))


def test_main_backup_system(results: TestResults):
    """Test 8: Main backup system integration."""
    print("\n🧪 Testing Main Backup System Integration...")

    try:
        # Test main backup script import
        try:
            from tngd_backup import TngdBackup
            results.add_test_result("Main Backup Script Import", True, "TngdBackup class imported successfully")
        except Exception as e:
            results.add_test_result("Main Backup Script Import", False, error=str(e))
            return

        # Test backup system initialization
        try:
            backup_system = TngdBackup()
            results.add_test_result("Backup System Initialization", True, "Backup system initialized successfully")
        except Exception as e:
            results.add_test_result("Backup System Initialization", False, error=str(e))
            return

        # Test connection testing
        try:
            if backup_system.test_connections():
                results.add_test_result("Connection Tests", True, "All connection tests passed")
            else:
                results.add_test_result("Connection Tests", False, "Some connection tests failed")
        except Exception as e:
            results.add_test_result("Connection Tests", False, error=str(e))

    except Exception as e:
        results.add_test_result("Main Backup System", False, error=str(e))


def test_system_resources(results: TestResults):
    """Test 9: System resource monitoring."""
    print("\n🧪 Testing System Resource Monitoring...")

    try:
        import psutil

        # Test CPU monitoring
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            if 0 <= cpu_percent <= 100:
                results.add_test_result("CPU Monitoring", True, f"CPU usage: {cpu_percent}%")
            else:
                results.add_test_result("CPU Monitoring", False, f"Invalid CPU reading: {cpu_percent}")
        except Exception as e:
            results.add_test_result("CPU Monitoring", False, error=str(e))

        # Test memory monitoring
        try:
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            if 0 <= memory_percent <= 100:
                results.add_test_result("Memory Monitoring", True, f"Memory usage: {memory_percent}%")
            else:
                results.add_test_result("Memory Monitoring", False, f"Invalid memory reading: {memory_percent}")
        except Exception as e:
            results.add_test_result("Memory Monitoring", False, error=str(e))

        # Test disk monitoring
        try:
            disk = psutil.disk_usage('.')
            disk_percent = (disk.used / disk.total) * 100
            if 0 <= disk_percent <= 100:
                results.add_test_result("Disk Monitoring", True, f"Disk usage: {disk_percent:.1f}%")
            else:
                results.add_test_result("Disk Monitoring", False, f"Invalid disk reading: {disk_percent}")
        except Exception as e:
            results.add_test_result("Disk Monitoring", False, error=str(e))

    except Exception as e:
        results.add_test_result("System Resource Monitoring", False, error=str(e))


if __name__ == "__main__":
    print("🚀 Starting Comprehensive TNGD System Tests")
    print("=" * 60)
    
    setup_logging()
    results = TestResults()
    
    # Run all tests
    test_core_module_imports(results)
    test_configuration_management(results)
    test_environment_variables(results)
    test_devo_client_connectivity(results)
    test_storage_manager_operations(results)
    test_email_service_functionality(results)
    test_compression_service(results)
    test_main_backup_system(results)
    test_system_resources(results)
    
    # Print summary
    summary = results.get_summary()
    print("\n" + "=" * 60)
    print(f"📊 Test Summary:")
    print(f"   Total Tests: {summary['total_tests']}")
    print(f"   Passed: {summary['passed_tests']}")
    print(f"   Failed: {summary['failed_tests']}")
    print(f"   Success Rate: {summary['success_rate']:.1f}%")
    print(f"   Duration: {summary['duration_seconds']:.2f} seconds")
    
    if summary['failed_tests'] == 0:
        print("\n🎉 All tests passed! System is ready for use.")
        sys.exit(0)
    else:
        print(f"\n⚠️  {summary['failed_tests']} test(s) failed. Please review the issues above.")
        sys.exit(1)
