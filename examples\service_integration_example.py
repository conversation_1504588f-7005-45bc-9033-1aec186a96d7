#!/usr/bin/env python3
"""
Service Integration Example

This example demonstrates how to use the new refactored services in the TNGD backup system.
It shows the integration of RetryService, ResourceMonitor, ErrorService, and ConfigService.
"""

import sys
from datetime import datetime
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import the new services
from core.config_manager import ConfigManager
from core.config_service import ConfigService
from core.retry_service import RetryService, RetryConfig, RetryStrategy
from core.resource_monitor import ResourceMonitor
from core.error_service import ErrorService, ErrorContext
from utils.error_handler import NetworkError, StorageError


class ModernBackupOperation:
    """
    Example class showing how to integrate the new services for backup operations.
    """
    
    def __init__(self):
        """Initialize with all the new services."""
        # Initialize configuration
        self.config_manager = ConfigManager()
        self.config_service = ConfigService(self.config_manager)
        
        # Initialize services
        self.retry_service = RetryService(self.config_manager)
        self.resource_monitor = ResourceMonitor(self.config_manager)
        self.error_service = ErrorService(self.config_manager)
        
        # Register cleanup callback with resource monitor
        self.resource_monitor.register_cleanup_callback(self._cleanup_resources)
        
        print("🚀 Modern backup operation initialized with new services")
    
    def backup_table_with_services(self, table_name: str, target_date: datetime):
        """
        Example backup operation using all the new services.
        
        Args:
            table_name: Name of the table to backup
            target_date: Target date for backup
        """
        print(f"\n📊 Starting backup for {table_name} on {target_date.date()}")
        
        # Create error context
        context = ErrorContext(
            operation="backup_table",
            component="backup_service",
            table_name=table_name,
            date=target_date.strftime("%Y-%m-%d")
        )
        
        try:
            # 1. Check resource usage and apply throttling
            print("🔍 Checking resource usage...")
            self.resource_monitor.apply_throttling(f"backup_{table_name}")
            
            metrics = self.resource_monitor.get_current_metrics()
            print(f"   CPU: {metrics.cpu_percent:.1f}%, Memory: {metrics.memory_percent:.1f}%, Disk: {metrics.disk_percent:.1f}%")
            
            # 2. Configure retry strategy based on configuration
            retry_config = RetryConfig(
                max_attempts=self.config_service.get_max_retry_attempts(),
                strategy=RetryStrategy.EXPONENTIAL_BACKOFF,
                base_delay_seconds=self.config_service.get_retry_base_delay(),
                max_delay_seconds=self.config_service.get_retry_max_delay(),
                jitter_enabled=True
            )
            
            # 3. Execute backup with retry logic
            print("🔄 Executing backup with retry logic...")
            result = self.retry_service.execute_with_retry(
                operation=lambda: self._simulate_backup_operation(table_name, target_date),
                operation_name=f"backup_{table_name}",
                retry_config=retry_config,
                circuit_breaker_name="backup_operations"
            )
            
            if result.success:
                print(f"✅ Backup completed successfully after {result.attempts_made} attempts")
                print(f"   Total delay: {result.total_delay:.1f}s")
                return result.result
            else:
                print(f"❌ Backup failed after {result.attempts_made} attempts")
                if result.circuit_breaker_triggered:
                    print("   Circuit breaker was triggered")
                
                # Handle error with error service
                if result.error:
                    error_record = self.error_service.handle_error(result.error, context)
                    print(f"   Error classified as: {error_record.category.value} ({error_record.severity.value})")
                    print(f"   Recovery strategy: {error_record.recovery_strategy.value if error_record.recovery_strategy else 'unknown'}")
                
                return None
                
        except Exception as e:
            # Handle unexpected errors
            error_record = self.error_service.handle_error(e, context)
            print(f"❌ Unexpected error: {error_record.error_message}")
            print(f"   Category: {error_record.category.value}")
            print(f"   Severity: {error_record.severity.value}")
            return None
    
    def _simulate_backup_operation(self, table_name: str, target_date: datetime):
        """
        Simulate a backup operation that might fail.

        This is just for demonstration - replace with actual backup logic.
        """
        import random
        import time

        # Simulate some processing time (shorter for demo)
        time.sleep(0.1)

        # Simulate different types of failures for demonstration
        # Reduced failure rates for better demo experience
        failure_chance = random.random()

        if failure_chance < 0.1:  # 10% chance of network error
            raise NetworkError("Connection timeout to Devo API", {
                "table_name": table_name,
                "error_code": "TIMEOUT"
            })
        elif failure_chance < 0.15:  # 5% chance of storage error
            raise StorageError("Insufficient disk space", {
                "table_name": table_name,
                "required_space_gb": 10.5
            })
        elif failure_chance < 0.2:  # 5% chance of generic error
            raise Exception("Temporary service unavailable")

        # Success case (80% chance)
        print(f"   ✅ Successfully processed {table_name}")
        return {
            "table_name": table_name,
            "date": target_date.strftime("%Y-%m-%d"),
            "rows_processed": random.randint(1000, 100000),
            "file_size_mb": random.uniform(1.0, 50.0),
            "processing_time": random.uniform(10.0, 120.0)
        }
    
    def _cleanup_resources(self):
        """Cleanup callback for resource monitor."""
        print("🧹 Performing resource cleanup...")
        # Add actual cleanup logic here
        # For example: clear caches, close connections, etc.
    
    def demonstrate_configuration_access(self):
        """Demonstrate the new configuration service."""
        print("\n⚙️  Configuration Service Demonstration:")
        
        # System configuration
        print(f"   Environment: {self.config_service.get_environment()}")
        print(f"   Version: {self.config_service.get_version()}")
        print(f"   Debug mode: {self.config_service.is_debug_mode()}")
        
        # Data source configuration
        print(f"   Data source provider: {self.config_service.get_data_source_provider()}")
        print(f"   Connection timeout: {self.config_service.get_connection_timeout()}s")
        print(f"   Retry attempts: {self.config_service.get_retry_attempts()}")
        
        # Processing configuration
        print(f"   Chunking enabled: {self.config_service.is_chunking_enabled()}")
        print(f"   Chunking strategy: {self.config_service.get_chunking_strategy()}")
        print(f"   Chunk time hours: {self.config_service.get_chunk_time_hours()}")
        
        # Resource management
        print(f"   Memory threshold: {self.config_service.get_memory_threshold()}%")
        print(f"   CPU threshold: {self.config_service.get_cpu_threshold()}%")
        print(f"   Adaptive throttling: {self.config_service.is_adaptive_throttling_enabled()}")
    
    def demonstrate_error_analysis(self):
        """Demonstrate error service capabilities."""
        print("\n🔍 Error Service Demonstration:")
        
        # Get error summary
        summary = self.error_service.get_error_summary()
        print(f"   Total errors recorded: {summary['total_errors']}")
        print(f"   Recent errors (last hour): {summary['recent_errors_count']}")
        print(f"   Critical errors: {summary['critical_errors_count']}")
        
        # Get recent errors
        recent_errors = self.error_service.get_recent_errors(minutes=60)
        if recent_errors:
            print(f"   Recent error categories:")
            for error in recent_errors[-3:]:  # Show last 3 errors
                print(f"     - {error.category.value}: {error.error_message[:50]}...")
    
    def demonstrate_resource_monitoring(self):
        """Demonstrate resource monitoring capabilities."""
        print("\n📊 Resource Monitor Demonstration:")
        
        # Get current metrics
        metrics = self.resource_monitor.get_current_metrics()
        print(f"   Current CPU usage: {metrics.cpu_percent:.1f}%")
        print(f"   Current memory usage: {metrics.memory_percent:.1f}% ({metrics.memory_mb:.1f}MB)")
        print(f"   Current disk usage: {metrics.disk_percent:.1f}% ({metrics.disk_free_gb:.1f}GB free)")
        print(f"   Alert level: {metrics.alert_level.value}")
        
        if metrics.throttle_delay > 0:
            print(f"   Recommended throttle delay: {metrics.throttle_delay:.1f}s")
        
        # Get recent alerts
        recent_alerts = self.resource_monitor.get_recent_alerts(minutes=60)
        if recent_alerts:
            print(f"   Recent alerts ({len(recent_alerts)}):")
            for alert in recent_alerts[-3:]:  # Show last 3 alerts
                print(f"     - {alert.resource_type.value}: {alert.message}")
    
    def demonstrate_retry_service(self):
        """Demonstrate retry service capabilities."""
        print("\n🔄 Retry Service Demonstration:")
        
        # Get retry service stats
        stats = self.retry_service.get_stats()
        print(f"   Circuit breakers active: {stats['total_circuit_breakers']}")
        
        if stats['circuit_breakers']:
            print("   Circuit breaker states:")
            for name, cb_stats in stats['circuit_breakers'].items():
                print(f"     - {name}: {cb_stats['state']} (failures: {cb_stats['failure_count']})")


def main():
    """Main demonstration function."""
    print("🎯 TNGD Backup System - Service Integration Example")
    print("=" * 60)
    
    try:
        # Initialize the modern backup operation
        backup_op = ModernBackupOperation()
        
        # Demonstrate configuration access
        backup_op.demonstrate_configuration_access()
        
        # Demonstrate resource monitoring
        backup_op.demonstrate_resource_monitoring()
        
        # Demonstrate retry service
        backup_op.demonstrate_retry_service()
        
        # Simulate backup operations
        print("\n🚀 Simulating Backup Operations:")
        tables = ["my.app.tngd.waf", "my.app.tngd.actiontraillinux"]
        target_date = datetime.now()
        
        for table in tables:
            result = backup_op.backup_table_with_services(table, target_date)
            if result:
                print(f"   Result: {result['rows_processed']:,} rows, {result['file_size_mb']:.1f}MB")
        
        # Demonstrate error analysis
        backup_op.demonstrate_error_analysis()
        
        print("\n🎉 Service integration demonstration completed!")
        
    except Exception as e:
        print(f"\n❌ Demonstration failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
