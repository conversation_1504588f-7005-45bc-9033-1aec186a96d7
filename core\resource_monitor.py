#!/usr/bin/env python3
"""
Resource Monitor Module

This module provides basic resource monitoring capabilities for the TNGD backup system.
It monitors CPU, memory, and disk usage to help optimize performance and prevent
system overload during large data processing operations.

Features:
- CPU, memory, and disk usage monitoring
- Configurable thresholds and alerts
- Adaptive throttling based on resource usage
- Integration with streaming data processing
"""

import time
import logging
import threading
from typing import Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime

# Configure logging
logger = logging.getLogger(__name__)

# Try to import psutil for system monitoring
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    logger.warning("psutil not available - resource monitoring will be limited")


@dataclass
class ResourceMetrics:
    """Resource usage metrics."""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_mb: float
    disk_percent: float
    disk_free_gb: float
    alert_level: str = "normal"
    throttle_delay: float = 0.0


class ResourceMonitor:
    """Basic resource monitoring for backup operations."""
    
    def __init__(self, config_manager=None):
        """
        Initialize resource monitor.
        
        Args:
            config_manager: Optional configuration manager
        """
        self.config_manager = config_manager
        self.logger = logging.getLogger(f"{__name__}.ResourceMonitor")
        
        # Default thresholds
        self.cpu_warning_threshold = 80.0
        self.cpu_critical_threshold = 95.0
        self.memory_warning_threshold = 80.0
        self.memory_critical_threshold = 90.0
        self.disk_warning_threshold = 85.0
        self.disk_critical_threshold = 95.0
        
        # Load thresholds from config if available
        if config_manager:
            self._load_config_thresholds()
        
        # Monitoring state
        self._last_metrics = None
        self._last_check_time = 0
        self._check_interval = 30  # seconds
        
        self.logger.info("Resource monitor initialized")
    
    def _load_config_thresholds(self):
        """Load threshold settings from configuration."""
        try:
            resource_config = self.config_manager.get('resource_management', {})
            
            # CPU thresholds
            cpu_config = resource_config.get('cpu', {})
            self.cpu_warning_threshold = cpu_config.get('threshold_percent', 80.0)
            self.cpu_critical_threshold = cpu_config.get('critical_threshold_percent', 95.0)
            
            # Memory thresholds
            memory_config = resource_config.get('memory', {})
            self.memory_warning_threshold = memory_config.get('threshold_percent', 80.0)
            self.memory_critical_threshold = memory_config.get('critical_threshold_percent', 90.0)
            
            # Disk thresholds
            disk_config = resource_config.get('disk', {})
            self.disk_warning_threshold = disk_config.get('threshold_percent', 85.0)
            self.disk_critical_threshold = disk_config.get('critical_threshold_percent', 95.0)
            
            self.logger.debug(f"Loaded thresholds - CPU: {self.cpu_warning_threshold}%/{self.cpu_critical_threshold}%, "
                            f"Memory: {self.memory_warning_threshold}%/{self.memory_critical_threshold}%, "
                            f"Disk: {self.disk_warning_threshold}%/{self.disk_critical_threshold}%")
        except Exception as e:
            self.logger.warning(f"Failed to load resource thresholds from config: {e}")
    
    def get_current_metrics(self) -> ResourceMetrics:
        """Get current resource usage metrics."""
        current_time = time.time()
        
        # Use cached metrics if recent enough
        if (self._last_metrics and 
            current_time - self._last_check_time < self._check_interval):
            return self._last_metrics
        
        if not PSUTIL_AVAILABLE:
            # Return default metrics if psutil is not available
            return ResourceMetrics(
                timestamp=datetime.now(),
                cpu_percent=0.0,
                memory_percent=0.0,
                memory_mb=0.0,
                disk_percent=0.0,
                disk_free_gb=0.0,
                alert_level="unknown"
            )
        
        try:
            # Get CPU usage
            cpu_percent = psutil.cpu_percent(interval=0.1)
            
            # Get memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_mb = memory.used / (1024 * 1024)
            
            # Get disk usage for current directory
            disk = psutil.disk_usage('.')
            disk_percent = (disk.used / disk.total) * 100
            disk_free_gb = disk.free / (1024 * 1024 * 1024)
            
            # Determine alert level
            alert_level = self._determine_alert_level(cpu_percent, memory_percent, disk_percent)
            
            # Calculate throttle delay if needed
            throttle_delay = self._calculate_throttle_delay(cpu_percent, memory_percent, disk_percent)
            
            metrics = ResourceMetrics(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_mb=memory_mb,
                disk_percent=disk_percent,
                disk_free_gb=disk_free_gb,
                alert_level=alert_level,
                throttle_delay=throttle_delay
            )
            
            # Cache metrics
            self._last_metrics = metrics
            self._last_check_time = current_time
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Error collecting resource metrics: {e}")
            # Return default metrics on error
            return ResourceMetrics(
                timestamp=datetime.now(),
                cpu_percent=0.0,
                memory_percent=0.0,
                memory_mb=0.0,
                disk_percent=0.0,
                disk_free_gb=0.0,
                alert_level="error"
            )
    
    def _determine_alert_level(self, cpu_percent: float, memory_percent: float, disk_percent: float) -> str:
        """Determine alert level based on resource usage."""
        # Check for critical levels
        if (cpu_percent >= self.cpu_critical_threshold or 
            memory_percent >= self.memory_critical_threshold or 
            disk_percent >= self.disk_critical_threshold):
            return "critical"
        
        # Check for warning levels
        if (cpu_percent >= self.cpu_warning_threshold or 
            memory_percent >= self.memory_warning_threshold or 
            disk_percent >= self.disk_warning_threshold):
            return "warning"
        
        return "normal"
    
    def _calculate_throttle_delay(self, cpu_percent: float, memory_percent: float, disk_percent: float) -> float:
        """Calculate throttle delay based on resource usage."""
        max_delay = 10.0  # Maximum 10 seconds delay
        
        # Calculate delay based on highest resource usage
        max_usage = max(
            cpu_percent / 100.0,
            memory_percent / 100.0,
            disk_percent / 100.0
        )
        
        # Apply throttling if usage is above warning threshold
        warning_threshold = min(
            self.cpu_warning_threshold,
            self.memory_warning_threshold,
            self.disk_warning_threshold
        ) / 100.0
        
        if max_usage > warning_threshold:
            # Linear scaling from warning threshold to max delay
            delay_factor = (max_usage - warning_threshold) / (1.0 - warning_threshold)
            return min(max_delay * delay_factor, max_delay)
        
        return 0.0
    
    def apply_throttling(self, operation_name: str = "operation"):
        """Apply adaptive throttling delay if needed."""
        metrics = self.get_current_metrics()
        
        if metrics.throttle_delay > 0:
            self.logger.info(f"Applying throttling delay of {metrics.throttle_delay:.1f}s for {operation_name} "
                           f"(CPU: {metrics.cpu_percent:.1f}%, Memory: {metrics.memory_percent:.1f}%, "
                           f"Disk: {metrics.disk_percent:.1f}%)")
            time.sleep(metrics.throttle_delay)
    
    def log_resource_status(self, operation_name: str = "operation"):
        """Log current resource status."""
        metrics = self.get_current_metrics()
        
        if metrics.alert_level == "critical":
            self.logger.warning(f"[{operation_name}] CRITICAL resource usage - "
                              f"CPU: {metrics.cpu_percent:.1f}%, Memory: {metrics.memory_percent:.1f}%, "
                              f"Disk: {metrics.disk_percent:.1f}%")
        elif metrics.alert_level == "warning":
            self.logger.info(f"[{operation_name}] HIGH resource usage - "
                           f"CPU: {metrics.cpu_percent:.1f}%, Memory: {metrics.memory_percent:.1f}%, "
                           f"Disk: {metrics.disk_percent:.1f}%")
        else:
            self.logger.debug(f"[{operation_name}] Resource usage - "
                            f"CPU: {metrics.cpu_percent:.1f}%, Memory: {metrics.memory_percent:.1f}%, "
                            f"Disk: {metrics.disk_percent:.1f}%")
